#!/bin/bash

# jCloud项目构建脚本
# 用于构建前后端项目并生成Docker镜像

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查命令是否存在
check_command() {
    if ! command -v $1 &> /dev/null; then
        log_error "$1 命令未找到，请先安装 $1"
        exit 1
    fi
}

# 检查必要的工具
check_dependencies() {
    log_info "检查构建依赖..."
    check_command "java"
    check_command "mvn"
    check_command "node"
    check_command "pnpm"
    check_command "docker"
    log_success "所有依赖检查通过"
}

# 清理旧的构建产物
clean_build() {
    log_info "清理旧的构建产物..."
    
    # 清理后端
    if [ -d "backend/target" ]; then
        rm -rf backend/target
        log_info "已清理后端构建目录"
    fi
    
    # 清理前端
    if [ -d "frontend/dist" ]; then
        rm -rf frontend/dist
        log_info "已清理前端构建目录"
    fi
    
    log_success "构建产物清理完成"
}

# 构建后端
build_backend() {
    log_info "开始构建后端项目..."
    
    cd backend
    
    # Maven构建
    log_info "执行Maven构建..."
    mvn clean package -DskipTests -B
    
    # 检查JAR文件是否生成
    if [ ! -f "jcloud-admin/target/jcloud-admin-1.0.0.jar" ]; then
        log_error "后端JAR文件构建失败"
        exit 1
    fi
    
    # 获取JAR文件大小
    jar_size=$(du -h jcloud-admin/target/jcloud-admin-1.0.0.jar | cut -f1)
    log_success "后端构建完成，JAR文件大小: $jar_size"
    
    cd ..
}

# 构建前端
build_frontend() {
    log_info "开始构建前端项目..."
    
    cd frontend
    
    # 安装依赖
    log_info "安装前端依赖..."
    pnpm install --frozen-lockfile
    
    # 构建项目
    log_info "执行前端构建..."
    pnpm run build
    
    # 检查构建产物
    if [ ! -d "dist" ]; then
        log_error "前端构建失败，dist目录不存在"
        exit 1
    fi
    
    # 获取构建产物大小
    dist_size=$(du -sh dist | cut -f1)
    log_success "前端构建完成，构建产物大小: $dist_size"
    
    cd ..
}

# 构建Docker镜像
build_docker_images() {
    log_info "开始构建Docker镜像..."
    
    # 构建后端镜像
    log_info "构建后端Docker镜像..."
    docker build -t jcloud/backend:1.0.0 -t jcloud/backend:latest ./backend
    
    # 构建前端镜像
    log_info "构建前端Docker镜像..."
    docker build -t jcloud/frontend:1.0.0 -t jcloud/frontend:latest ./frontend
    
    log_success "Docker镜像构建完成"
    
    # 显示镜像信息
    log_info "Docker镜像列表:"
    docker images | grep jcloud
}

# 验证构建结果
verify_build() {
    log_info "验证构建结果..."
    
    # 检查后端JAR文件
    if [ -f "backend/jcloud-admin/target/jcloud-admin-1.0.0.jar" ]; then
        log_success "✓ 后端JAR文件存在"
    else
        log_error "✗ 后端JAR文件不存在"
        exit 1
    fi
    
    # 检查前端构建产物
    if [ -d "frontend/dist" ] && [ -f "frontend/dist/index.html" ]; then
        log_success "✓ 前端构建产物存在"
    else
        log_error "✗ 前端构建产物不存在"
        exit 1
    fi
    
    # 检查Docker镜像
    if docker images | grep -q "jcloud/backend"; then
        log_success "✓ 后端Docker镜像存在"
    else
        log_error "✗ 后端Docker镜像不存在"
        exit 1
    fi
    
    if docker images | grep -q "jcloud/frontend"; then
        log_success "✓ 前端Docker镜像存在"
    else
        log_error "✗ 前端Docker镜像不存在"
        exit 1
    fi
    
    log_success "所有构建产物验证通过"
}

# 主函数
main() {
    log_info "开始jCloud项目构建流程..."
    
    # 记录开始时间
    start_time=$(date +%s)
    
    # 执行构建步骤
    check_dependencies
    clean_build
    build_backend
    build_frontend
    build_docker_images
    verify_build
    
    # 计算构建时间
    end_time=$(date +%s)
    build_time=$((end_time - start_time))
    
    log_success "🎉 jCloud项目构建完成！"
    log_info "总构建时间: ${build_time}秒"
    log_info ""
    log_info "下一步可以执行:"
    log_info "  1. 运行测试: ./scripts/test.sh"
    log_info "  2. 启动服务: ./scripts/deploy.sh"
    log_info "  3. 查看日志: docker-compose logs -f"
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
