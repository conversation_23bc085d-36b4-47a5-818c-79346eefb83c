# jCloud项目Docker Compose环境变量配置文件
# 复制此文件为 .env.local 并根据实际部署环境修改配置

# ==================== 数据库配置 ====================
# MySQL root密码
MYSQL_ROOT_PASSWORD=jcloud123456

# MySQL数据库名
MYSQL_DATABASE=jcloud

# MySQL用户名
MYSQL_USER=jcloud

# MySQL用户密码
MYSQL_PASSWORD=jcloud123456

# MySQL端口映射
MYSQL_PORT=3306

# ==================== Redis配置 ====================
# Redis密码
REDIS_PASSWORD=jcloud123456

# Redis端口映射
REDIS_PORT=6379

# ==================== 应用端口配置 ====================
# 后端服务端口
BACKEND_PORT=8081

# 前端服务端口
FRONTEND_PORT=80

# Nginx反向代理端口（生产环境使用）
NGINX_PORT=8080

# ==================== 部署环境配置 ====================
# 部署环境标识
DEPLOY_ENV=production

# 应用版本
APP_VERSION=1.0.0

# 构建时间
BUILD_TIME=2024-01-01T00:00:00Z
