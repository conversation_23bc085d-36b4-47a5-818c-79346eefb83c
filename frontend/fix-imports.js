#!/usr/bin/env node

/**
 * 修复前端项目中的导入路径问题
 * 
 * 主要修复：
 * 1. 移除.ts/.tsx扩展名
 * 2. 统一使用@/路径别名
 * 3. 修复相对路径问题
 */

const fs = require('fs')
const path = require('path')

// 需要处理的文件扩展名
const FILE_EXTENSIONS = ['.ts', '.tsx', '.js', '.jsx']

// 需要修复的导入模式
const IMPORT_PATTERNS = [
  // 移除.ts/.tsx扩展名
  {
    pattern: /from\s+['"]([^'"]+)\.tsx?['"]/g,
    replacement: "from '$1'"
  },
  // 修复相对路径导入
  {
    pattern: /from\s+['"]\.\.\/\.\.\/\.\.\/\.\.\/services\/([^'"]+)['"]/g,
    replacement: "from '@/services/$1'"
  },
  {
    pattern: /from\s+['"]\.\.\/\.\.\/\.\.\/services\/([^'"]+)['"]/g,
    replacement: "from '@/services/$1'"
  },
  {
    pattern: /from\s+['"]\.\.\/\.\.\/services\/([^'"]+)['"]/g,
    replacement: "from '@/services/$1'"
  },
  {
    pattern: /from\s+['"]\.\.\/services\/([^'"]+)['"]/g,
    replacement: "from '@/services/$1'"
  }
]

/**
 * 递归获取所有需要处理的文件
 */
function getAllFiles(dir, fileList = []) {
  const files = fs.readdirSync(dir)
  
  files.forEach(file => {
    const filePath = path.join(dir, file)
    const stat = fs.statSync(filePath)
    
    if (stat.isDirectory()) {
      // 跳过node_modules和dist目录
      if (!['node_modules', 'dist', '.git'].includes(file)) {
        getAllFiles(filePath, fileList)
      }
    } else if (FILE_EXTENSIONS.includes(path.extname(file))) {
      fileList.push(filePath)
    }
  })
  
  return fileList
}

/**
 * 修复单个文件的导入
 */
function fixFileImports(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8')
    let modified = false
    
    // 应用所有修复模式
    IMPORT_PATTERNS.forEach(({ pattern, replacement }) => {
      const newContent = content.replace(pattern, replacement)
      if (newContent !== content) {
        content = newContent
        modified = true
      }
    })
    
    // 如果文件被修改，写回文件
    if (modified) {
      fs.writeFileSync(filePath, content, 'utf8')
      console.log(`✅ 修复: ${filePath}`)
      return true
    }
    
    return false
  } catch (error) {
    console.error(`❌ 处理文件失败: ${filePath}`, error.message)
    return false
  }
}

/**
 * 主函数
 */
function main() {
  console.log('🔧 开始修复前端导入路径问题...\n')
  
  const srcDir = path.join(__dirname, 'src')
  const files = getAllFiles(srcDir)
  
  console.log(`📁 找到 ${files.length} 个文件需要检查\n`)
  
  let fixedCount = 0
  
  files.forEach(file => {
    if (fixFileImports(file)) {
      fixedCount++
    }
  })
  
  console.log(`\n🎉 修复完成！`)
  console.log(`📊 总计修复 ${fixedCount} 个文件`)
  
  if (fixedCount > 0) {
    console.log('\n📋 修复内容：')
    console.log('✅ 移除了所有.ts/.tsx扩展名')
    console.log('✅ 统一了相对路径为@/路径别名')
    console.log('✅ 修复了深层嵌套的相对导入')
  } else {
    console.log('\n✨ 所有文件的导入路径都是正确的！')
  }
}

// 运行修复
if (require.main === module) {
  main()
}

module.exports = { fixFileImports, getAllFiles, IMPORT_PATTERNS }
