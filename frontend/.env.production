# jCloud前端生产环境配置

# 应用标题
VITE_APP_TITLE=jCloud权限管理系统

# API基础URL - 生产环境
VITE_API_BASE_URL=/api

# 应用版本
VITE_APP_VERSION=1.0.0

# 构建时间戳
VITE_BUILD_TIME=__BUILD_TIME__

# 是否启用Mock数据
VITE_USE_MOCK=false

# 是否启用开发工具
VITE_ENABLE_DEVTOOLS=false

# 是否启用错误监控
VITE_ENABLE_ERROR_MONITOR=true

# 是否启用性能监控
VITE_ENABLE_PERFORMANCE_MONITOR=true

# 日志级别 (error, warn, info, debug)
VITE_LOG_LEVEL=warn

# 是否启用PWA
VITE_ENABLE_PWA=false

# CDN基础URL（如果使用CDN）
VITE_CDN_BASE_URL=

# 文件上传大小限制（MB）
VITE_UPLOAD_MAX_SIZE=10

# 支持的文件类型
VITE_UPLOAD_ALLOWED_TYPES=jpg,jpeg,png,gif,pdf,doc,docx,xls,xlsx

# 分页默认大小
VITE_DEFAULT_PAGE_SIZE=20

# Token存储键名
VITE_TOKEN_KEY=jcloud_token

# 用户信息存储键名
VITE_USER_INFO_KEY=jcloud_user_info

# 主题存储键名
VITE_THEME_KEY=jcloud_theme

# 语言存储键名
VITE_LOCALE_KEY=jcloud_locale

# 默认语言
VITE_DEFAULT_LOCALE=zh-CN

# 是否启用国际化
VITE_ENABLE_I18N=false

# 路由模式 (hash | history)
VITE_ROUTER_MODE=history

# 路由基础路径
VITE_ROUTER_BASE=/

# 是否启用路由缓存
VITE_ENABLE_ROUTE_CACHE=true

# 缓存最大数量
VITE_CACHE_MAX_COUNT=10

# 请求超时时间（毫秒）
VITE_REQUEST_TIMEOUT=10000

# 重试次数
VITE_REQUEST_RETRY_COUNT=3

# 重试延迟（毫秒）
VITE_REQUEST_RETRY_DELAY=1000
