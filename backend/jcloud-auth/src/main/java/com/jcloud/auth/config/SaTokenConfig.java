package com.jcloud.auth.config;

import cn.dev33.satoken.context.SaHolder;
import cn.dev33.satoken.interceptor.SaInterceptor;
import cn.dev33.satoken.router.SaRouter;
import cn.dev33.satoken.stp.StpUtil;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * Sa-Token配置类 - 修复认证错误问题
 * 支持注解权限验证和拦截器登录验证
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Configuration

public class SaTokenConfig implements WebMvcConfigurer {

    /**
     * 注册Sa-Token拦截器
     * 注意：Sa-Token的@SaCheckPermission注解是通过Spring Boot自动配置的AOP处理的
     * 这里的拦截器主要用于基础的登录检查和路径排除
     */
    @SuppressWarnings("null")
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // 注册Sa-Token拦截器，进行基础的登录检查
        registry.addInterceptor(new SaInterceptor(handle -> {
            SaRouter
                .match("/**")  // 拦截所有路径
                .notMatch("/auth/login", "/auth/logout", "/auth/captcha/**")  // 排除认证相关接口
                .notMatch("/", "/favicon.ico")  // 排除静态资源
                .notMatch("/doc.html", "/webjars/**", "/swagger-resources/**", "/v3/api-docs/**")  // 排除API文档
                .notMatch("/actuator/**")  // 排除监控端点
                .notMatch("/test/**")  // 排除测试接口
                .notMatch("/error")  // 排除错误页面
                .check(r -> {
                    // 跳过OPTIONS预检请求，避免干扰CORS
                    String method = SaHolder.getRequest().getMethod();
                    if ("OPTIONS".equalsIgnoreCase(method)) {
                        return;
                    }
                    // 执行登录校验 - 注意：权限校验由@SaCheckPermission注解处理
                    StpUtil.checkLogin();
                });
        }))
                .addPathPatterns("/**")
                // 设置拦截器优先级，确保在其他拦截器之前执行
                .order(-100);
    }
}