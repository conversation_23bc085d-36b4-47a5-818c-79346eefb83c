<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 
         http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.jcloud</groupId>
    <artifactId>jcloud-parent</artifactId>
    <version>1.0.0</version>
    <packaging>pom</packaging>
    <name>jCloud权限管理系统</name>
    <description>企业级权限管理系统父项目</description>

    <modules>
        <module>jcloud-common</module>
        <module>jcloud-auth</module>
        <module>jcloud-admin</module>
    </modules>

    <properties>
        <maven.compiler.source>21</maven.compiler.source>
        <maven.compiler.target>21</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        
        <!-- Spring Boot版本 -->
        <spring-boot.version>3.2.1</spring-boot.version>
        <!-- sa-token版本 -->
        <sa-token.version>1.37.0</sa-token.version>
        <!-- MyBatis-Flex版本 -->
        <mybatis-flex.version>1.8.0</mybatis-flex.version>
        <!-- Hutool版本 -->
        <hutool.version>5.8.24</hutool.version>
        <!-- Knife4j版本 -->
        <knife4j.version>4.5.0</knife4j.version>
        <!-- FastJSON2版本 -->
        <fastjson2.version>2.0.57</fastjson2.version>
        <!-- MySQL驱动版本 -->
        <mysql.version>8.0.33</mysql.version>
        <!-- Druid版本 -->
        <druid.version>1.2.20</druid.version>
        <!-- Swagger版本 -->
        <swagger.version>2.2.19</swagger.version>
        <!-- Redis版本 -->
        <jedis.version>5.1.0</jedis.version>
        <!-- Lombok版本 -->
        <lombok.version>1.18.30</lombok.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <!-- Spring Boot BOM -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- sa-token权限认证 -->
            <dependency>
                <groupId>cn.dev33</groupId>
                <artifactId>sa-token-spring-boot3-starter</artifactId>
                <version>${sa-token.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.dev33</groupId>
                <artifactId>sa-token-redis-jackson</artifactId>
                <version>${sa-token.version}</version>
            </dependency>

            <!-- MyBatis-Flex -->
            <dependency>
                <groupId>com.mybatis-flex</groupId>
                <artifactId>mybatis-flex-spring-boot3-starter</artifactId>
                <version>${mybatis-flex.version}</version>
            </dependency>
            <dependency>
                <groupId>com.mybatis-flex</groupId>
                <artifactId>mybatis-flex-codegen</artifactId>
                <version>${mybatis-flex.version}</version>
            </dependency>

            <!-- 数据库驱动 -->
            <dependency>
                <groupId>com.mysql</groupId>
                <artifactId>mysql-connector-j</artifactId>
                <version>${mysql.version}</version>
            </dependency>

            <!-- Druid数据库连接池 -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid-spring-boot-3-starter</artifactId>
                <version>${druid.version}</version>
            </dependency>

            <!-- Redis客户端 -->
            <dependency>
                <groupId>redis.clients</groupId>
                <artifactId>jedis</artifactId>
                <version>${jedis.version}</version>
            </dependency>

            <!-- Hutool工具类 -->
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>${hutool.version}</version>
            </dependency>

            <!-- Knife4j API文档 -->
            <dependency>
                <groupId>com.github.xiaoymin</groupId>
                <artifactId>knife4j-openapi3-jakarta-spring-boot-starter</artifactId>
                <version>${knife4j.version}</version>
            </dependency>

            <!-- Swagger注解 -->
            <dependency>
                <groupId>io.swagger.core.v3</groupId>
                <artifactId>swagger-annotations</artifactId>
                <version>${swagger.version}</version>
            </dependency>

            <!-- Lombok -->
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
                <scope>provided</scope>
            </dependency>

            <!-- FastJSON2 Spring Boot扩展 -->
            <dependency>
                <groupId>com.alibaba.fastjson2</groupId>
                <artifactId>fastjson2-extension-spring6</artifactId>
                <version>${fastjson2.version}</version>
            </dependency>

            <!-- 项目内部模块 -->
            <dependency>
                <groupId>com.jcloud</groupId>
                <artifactId>jcloud-common</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.jcloud</groupId>
                <artifactId>jcloud-auth</artifactId>
                <version>${project.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.11.0</version>
                <configuration>
                    <source>21</source>
                    <target>21</target>
                    <encoding>UTF-8</encoding>
                    <!-- 保留参数名信息，解决MyBatis参数映射问题 -->
                    <parameters>true</parameters>
                    <compilerArgs>
                        <arg>-parameters</arg>
                    </compilerArgs>
                    <!-- 配置注解处理器路径 -->
                    <annotationProcessorPaths>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <version>${lombok.version}</version>
                        </path>
                    </annotationProcessorPaths>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
