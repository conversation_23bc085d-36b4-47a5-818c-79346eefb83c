package com.jcloud.common.config;

import com.alibaba.fastjson2.JSONReader;
import com.alibaba.fastjson2.JSONWriter;
import com.alibaba.fastjson2.support.config.FastJsonConfig;
import com.alibaba.fastjson2.support.spring6.http.converter.FastJsonHttpMessageConverter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.http.MediaType;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.nio.charset.StandardCharsets;
import java.util.Collections;
import java.util.List;

/**
 * FastJSON2配置类
 * 替换默认的Jackson，解决LocalDateTime序列化问题
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Configuration
public class FastJson2Config implements WebMvcConfigurer {

    /**
     * 配置FastJSON2的HTTP消息转换器
     */
    @Override
    public void configureMessageConverters(List<HttpMessageConverter<?>> converters) {
        log.info("配置FastJSON2 HTTP消息转换器");
        
        // 创建FastJSON2转换器
        FastJsonHttpMessageConverter converter = new FastJsonHttpMessageConverter();
        
        // 创建FastJSON2配置
        FastJsonConfig config = new FastJsonConfig();
        
        // 配置序列化特性
        config.setWriterFeatures(
            JSONWriter.Feature.WriteMapNullValue,        // 输出null值
            JSONWriter.Feature.WriteNullStringAsEmpty,   // null字符串输出为空字符串
            JSONWriter.Feature.WriteNullListAsEmpty,     // null集合输出为空集合
            JSONWriter.Feature.WriteLongAsString,        // Long类型输出为字符串
            JSONWriter.Feature.WriteEnumUsingToString,   // 枚举使用toString方法
            JSONWriter.Feature.PrettyFormat              // 格式化输出
        );
        
        // 配置反序列化特性
        config.setReaderFeatures(
            JSONReader.Feature.FieldBased,               // 基于字段
            JSONReader.Feature.SupportArrayToBean       // 支持数组转Bean
        );
        
        // 设置日期格式
        config.setDateFormat("yyyy-MM-dd HH:mm:ss");
        
        // 设置字符集
        converter.setDefaultCharset(StandardCharsets.UTF_8);
        
        // 设置支持的媒体类型
        converter.setSupportedMediaTypes(Collections.singletonList(MediaType.APPLICATION_JSON));
        
        // 应用配置
        converter.setFastJsonConfig(config);
        
        // 将FastJSON2转换器添加到转换器列表的第一位
        converters.addFirst(converter);
        
        log.info("FastJSON2 HTTP消息转换器配置完成");
    }
    
    /**
     * 创建FastJSON2配置Bean
     * 供其他组件使用
     */
    @Bean
    @Primary
    public FastJsonConfig fastJsonConfig() {
        log.info("创建FastJSON2全局配置Bean");
        
        FastJsonConfig config = new FastJsonConfig();
        
        // 配置序列化特性
        config.setWriterFeatures(
            // 输出null值
            JSONWriter.Feature.WriteMapNullValue,
            // null字符串输出为空字符串
            JSONWriter.Feature.WriteNullStringAsEmpty,
            // null集合输出为空集合
            JSONWriter.Feature.WriteNullListAsEmpty,
            // Long类型输出为字符串
            JSONWriter.Feature.WriteLongAsString,
            // 枚举使用toString方法
            JSONWriter.Feature.WriteEnumUsingToString
        );
        
        // 配置反序列化特性
        config.setReaderFeatures(
                // 基于字段
            JSONReader.Feature.FieldBased,
                // 支持数组转Bean
            JSONReader.Feature.SupportArrayToBean
        );
        
        // 设置日期格式
        config.setDateFormat("yyyy-MM-dd HH:mm:ss");
        
        log.info("FastJSON2全局配置Bean创建完成");
        return config;
    }
}
