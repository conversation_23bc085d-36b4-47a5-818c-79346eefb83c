package com.jcloud.common.config;

import com.alibaba.fastjson2.JSONReader;
import com.alibaba.fastjson2.JSONWriter;
import com.alibaba.fastjson2.reader.ObjectReader;
import com.alibaba.fastjson2.writer.ObjectWriter;
import com.jcloud.common.util.TimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;

import jakarta.annotation.PostConstruct;
import java.lang.reflect.Type;

/**
 * 时间戳配置类
 * 配置FastJSON2对时间戳字段的序列化和反序列化
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Configuration
public class TimeStampConfig {

    /**
     * 配置时间戳的序列化和反序列化
     */
    @PostConstruct
    public void configureTimestamp() {
        log.info("配置时间戳序列化器");
        
        // 注册时间戳序列化器（将时间戳转换为格式化字符串用于前端显示）
        com.alibaba.fastjson2.JSON.register(Long.class, new ObjectWriter<Long>() {
            @Override
            public void write(JSONWriter jsonWriter, Object object, Object fieldName, Type fieldType, long features) {
                Long timestamp = (Long) object;
                if (timestamp == null) {
                    jsonWriter.writeNull();
                    return;
                }
                
                // 如果字段名包含Time，则格式化为时间字符串
                if (fieldName != null && fieldName.toString().toLowerCase().contains("time")) {
                    jsonWriter.writeString(TimeUtil.format(timestamp));
                } else {
                    jsonWriter.writeInt64(timestamp);
                }
            }
        });
        
        // 注册时间戳反序列化器（支持时间字符串转时间戳）
        com.alibaba.fastjson2.JSON.register(Long.class, new ObjectReader<Long>() {
            @Override
            public Long readObject(JSONReader jsonReader, Type fieldType, Object fieldName, long features) {
                if (jsonReader.isNull()) {
                    return null;
                }
                
                // 如果是字符串，尝试解析为时间戳
                if (jsonReader.isString()) {
                    String timeStr = jsonReader.readString();
                    try {
                        return TimeUtil.parse(timeStr);
                    } catch (Exception e) {
                        log.warn("解析时间字符串失败: {}", timeStr, e);
                        return null;
                    }
                }
                
                // 否则直接读取数字
                return jsonReader.readInt64();
            }
        });
        
        log.info("时间戳序列化器配置完成");
    }
}
