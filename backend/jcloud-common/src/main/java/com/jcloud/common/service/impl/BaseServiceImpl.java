package com.jcloud.common.service.impl;

import com.jcloud.common.entity.BaseEntity;
import com.jcloud.common.page.PageQuery;
import com.jcloud.common.page.PageResult;
import com.jcloud.common.service.BaseService;
import com.jcloud.common.util.SecurityUtils;
import com.jcloud.common.constant.CommonConstants;
import com.mybatisflex.core.BaseMapper;
import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.Serializable;
import com.jcloud.common.util.TimeUtil;
import java.util.List;

/**
 * 基础服务实现类
 * 实现通用的CRUD操作，自动处理多租户数据隔离
 * 
 * @param <M> Mapper类型
 * @param <T> 实体类型
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
public abstract class BaseServiceImpl<M extends BaseMapper<T>, T extends BaseEntity> implements BaseService<T> {
    
    @Autowired
    protected M baseMapper;
    
    /**
     * 获取当前实体的查询包装器，自动添加租户过滤条件
     */
    protected QueryWrapper getQueryWrapper() {
        QueryWrapper queryWrapper = QueryWrapper.create();
        // 自动添加租户过滤条件（超级管理员除外）
        if (!SecurityUtils.isSuperAdmin()) {
            Long tenantId = SecurityUtils.getTenantId();
            if (tenantId != null) {
                queryWrapper.eq("tenant_id", tenantId);
            }
        }
        // 自动添加逻辑删除过滤条件
        queryWrapper.eq("deleted", CommonConstants.NOT_DELETED);
        return queryWrapper;
    }
    
    /**
     * 获取不过滤租户的查询包装器（仅超级管理员可用）
     */
    protected QueryWrapper getQueryWrapperWithoutTenant() {
        QueryWrapper queryWrapper = QueryWrapper.create();
        // 只添加逻辑删除过滤条件
        queryWrapper.eq("deleted", CommonConstants.NOT_DELETED);
        return queryWrapper;
    }
    
    /**
     * 设置实体的基础信息（租户ID、创建人、更新人等）
     */
    protected void setBaseInfo(T entity, boolean isUpdate) {
        Long currentUserId = SecurityUtils.getUserId();
        Long tenantId = SecurityUtils.getTenantId();
        Long now = TimeUtil.now();
        
        if (!isUpdate) {
            // 新增时设置创建信息
            entity.setCreateBy(currentUserId);
            entity.setCreateTime(now);
            entity.setTenantId(tenantId);
        }
        
        // 设置更新信息
        entity.setUpdateBy(currentUserId);
        entity.setUpdateTime(now);
    }
    
    @Override
    public T getById(Serializable id) {
        QueryWrapper queryWrapper = getQueryWrapper().eq("id", id);
        return baseMapper.selectOneByQuery(queryWrapper);
    }
    
    @Override
    public T getByIdWithoutTenant(Serializable id) {
        if (!SecurityUtils.isSuperAdmin()) {
            throw new SecurityException("只有超级管理员才能跨租户查询数据");
        }
        QueryWrapper queryWrapper = getQueryWrapperWithoutTenant().eq("id", id);
        return baseMapper.selectOneByQuery(queryWrapper);
    }
    
    @Override
    public List<T> list() {
        QueryWrapper queryWrapper = getQueryWrapper();
        return baseMapper.selectListByQuery(queryWrapper);
    }
    
    @Override
    public List<T> list(T entity) {
        QueryWrapper queryWrapper = getQueryWrapper();
        // 这里可以根据实体属性动态构建查询条件
        // 具体实现可以在子类中重写
        return baseMapper.selectListByQuery(queryWrapper);
    }
    
    @Override
    public PageResult<T> page(PageQuery pageQuery) {
        QueryWrapper queryWrapper = getQueryWrapper();
        
        // 添加排序条件
        if (pageQuery.getOrderBy() != null && !pageQuery.getOrderBy().isEmpty()) {
            if (CommonConstants.ORDER_ASC.equalsIgnoreCase(pageQuery.getOrderDirection())) {
                queryWrapper.orderBy(pageQuery.getOrderBy(), true);
            } else {
                queryWrapper.orderBy(pageQuery.getOrderBy(), false);
            }
        } else {
            // 默认按创建时间降序排列
            queryWrapper.orderBy("create_time", false);
        }
        
        Page<T> page = Page.of(pageQuery.getPageNum(), pageQuery.getPageSize());
        Page<T> result = baseMapper.paginate(page, queryWrapper);
        
        return PageResult.of(result.getRecords(), result.getTotalRow(), 
                           pageQuery.getPageNum(), pageQuery.getPageSize());
    }
    
    @Override
    public PageResult<T> page(PageQuery pageQuery, T entity) {
        // 子类可以重写此方法来实现具体的条件查询
        return page(pageQuery);
    }
    
    @Override
    public boolean save(T entity) {
        if (entity.getId() == null) {
            // 新增
            setBaseInfo(entity, false);
            return baseMapper.insert(entity) > 0;
        } else {
            // 更新
            setBaseInfo(entity, true);
            return baseMapper.update(entity) > 0;
        }
    }
    
    @Override
    public boolean saveBatch(List<T> entityList) {
        if (entityList == null || entityList.isEmpty()) {
            return true;
        }
        
        for (T entity : entityList) {
            setBaseInfo(entity, entity.getId() != null);
        }
        
        return baseMapper.insertBatch(entityList) > 0;
    }
    
    @Override
    public boolean updateById(T entity) {
        if (entity.getId() == null) {
            throw new IllegalArgumentException("更新实体时ID不能为空");
        }
        
        setBaseInfo(entity, true);
        
        QueryWrapper queryWrapper = getQueryWrapper().eq("id", entity.getId());
        return baseMapper.updateByQuery(entity, queryWrapper) > 0;
    }
    
    @Override
    public boolean updateBatchById(List<T> entityList) {
        if (entityList == null || entityList.isEmpty()) {
            return true;
        }

        for (T entity : entityList) {
            if (entity.getId() == null) {
                throw new IllegalArgumentException("批量更新实体时ID不能为空");
            }
            setBaseInfo(entity, true);
            updateById(entity);
        }

        return true;
    }
    
    @Override
    public boolean removeById(Serializable id) {
        T entity = getById(id);
        if (entity == null) {
            return false;
        }
        
        // 逻辑删除
        entity.setDeleted(CommonConstants.DELETED);
        setBaseInfo(entity, true);
        
        return baseMapper.update(entity) > 0;
    }
    
    @Override
    public boolean removeByIds(List<? extends Serializable> idList) {
        if (idList == null || idList.isEmpty()) {
            return true;
        }
        
        for (Serializable id : idList) {
            removeById(id);
        }
        
        return true;
    }
    
    @Override
    public boolean remove(T entity) {
        QueryWrapper queryWrapper = getQueryWrapper();
        // 这里可以根据实体属性动态构建删除条件
        // 具体实现可以在子类中重写

        // 逻辑删除 - 直接使用传入的实体
        entity.setDeleted(CommonConstants.DELETED);
        setBaseInfo(entity, true);

        return baseMapper.updateByQuery(entity, queryWrapper) > 0;
    }
    
    @Override
    public boolean deleteById(Serializable id) {
        log.warn("执行物理删除操作，ID: {}, 操作人: {}", id, SecurityUtils.getUsername());
        QueryWrapper queryWrapper = getQueryWrapper().eq("id", id);
        return baseMapper.deleteByQuery(queryWrapper) > 0;
    }
    
    @Override
    public long count() {
        QueryWrapper queryWrapper = getQueryWrapper();
        return baseMapper.selectCountByQuery(queryWrapper);
    }
    
    @Override
    public long count(T entity) {
        QueryWrapper queryWrapper = getQueryWrapper();
        // 这里可以根据实体属性动态构建统计条件
        // 具体实现可以在子类中重写
        return baseMapper.selectCountByQuery(queryWrapper);
    }
    
    @Override
    public boolean exists(Serializable id) {
        return getById(id) != null;
    }
    
    @Override
    public boolean exists(T entity) {
        QueryWrapper queryWrapper = getQueryWrapper();
        // 这里可以根据实体属性动态构建存在性检查条件
        // 具体实现可以在子类中重写
        return baseMapper.selectCountByQuery(queryWrapper) > 0;
    }
}
