-- 测试GetInviteUserStatistics存储过程的新字段
-- 验证period_new_invite_count字段是否正常返回

-- 示例调用存储过程
-- 参数说明：
-- p_invite_user_id: 邀请用户ID（主播ID）
-- p_start_time: 开始时间（秒级时间戳）
-- p_end_time: 结束时间（秒级时间戳）

-- 测试调用示例（请根据实际数据调整参数）
CALL GetInviteUserStatistics(1, 1640995200, 1672531199);

-- 预期结果应包含以下字段：
-- total_recharge: 总充值金额
-- total_consume: 总消费金额
-- user_count: 用户总数
-- period_new_user_count: 指定时间区间内新增用户数
-- period_new_invite_count: 指定时间区间内新邀请的下级用户总数（新增字段）
-- total_claim_amount: 总待发货金额
-- total_shipped_amount: 总实际发货金额
-- total_backpack_amount: 背包总价值
-- period_total_recharge: 时间区间内下级用户总充值金额
-- total_turnover: 总流水
-- profit_ratio: 利润比
-- actual_profit: 实际利润金额
-- start_time: 查询开始时间
-- end_time: 查询结束时间

-- 验证新字段的查询逻辑
-- 单独测试period_new_invite_count的计算逻辑
SELECT 
    COUNT(*) AS period_new_invite_count_test
FROM vim_user 
WHERE invite_user = 1  -- 替换为实际的邀请用户ID
  AND create_time >= 1640995200  -- 替换为实际的开始时间
  AND create_time <= 1672531199; -- 替换为实际的结束时间
