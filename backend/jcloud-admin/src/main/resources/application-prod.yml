# jCloud生产环境配置文件
server:
  port: ${SERVER_PORT:8081}
  servlet:
    context-path: /api
    encoding:
      charset: UTF-8
      enabled: true
      force: true
  # 生产环境性能优化
  tomcat:
    threads:
      max: 200
      min-spare: 10
    max-connections: 8192
    accept-count: 100
    connection-timeout: 20000
    max-http-form-post-size: 2MB

spring:
  application:
    name: jcloud-admin

  # 数据源配置 - 生产环境
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: jdbc:mysql://${DB_HOST:localhost}:${DB_PORT:3306}/${DB_NAME:jcloud}?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=${DB_SSL:false}&serverTimezone=GMT%2B8&allowPublicKeyRetrieval=true
    username: ${DB_USERNAME:root}
    password: ${DB_PASSWORD:password}
    
    # Druid连接池配置 - 生产环境优化
    druid:
      initial-size: 10
      min-idle: 10
      max-active: 50
      max-wait: 60000
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      validation-query: SELECT 1
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      pool-prepared-statements: true
      max-pool-prepared-statement-per-connection-size: 50
      filters: stat,wall,slf4j
      connection-properties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=1500
      query-timeout: 15
      remove-abandoned: true
      remove-abandoned-timeout: 600
      log-abandoned: false
      keep-alive: true
      phy-timeout-millis: 600000
      
      # 生产环境监控配置
      web-stat-filter:
        enabled: true
        url-pattern: /*
        exclusions: "*.js,*.gif,*.jpg,*.png,*.css,*.ico,/druid/*"
      stat-view-servlet:
        enabled: ${DRUID_MONITOR_ENABLED:false}
        url-pattern: /druid/*
        reset-enable: false
        login-username: ${DRUID_USERNAME:admin}
        login-password: ${DRUID_PASSWORD:admin123}
        allow: ${DRUID_ALLOW_IPS:127.0.0.1}

  # Redis配置 - 生产环境
  data:
    redis:
      host: ${REDIS_HOST:localhost}
      port: ${REDIS_PORT:6379}
      password: ${REDIS_PASSWORD:}
      database: ${REDIS_DATABASE:10}
      timeout: 10s
      connect-timeout: 5s
      lettuce:
        pool:
          max-active: 50
          max-wait: 5000ms
          max-idle: 20
          min-idle: 10
        shutdown-timeout: 100ms
        cluster:
          refresh:
            adaptive: true
            period: 30s

  # JPA配置 - 生产环境
  jpa:
    hibernate:
      ddl-auto: none
    show-sql: false
    properties:
      hibernate:
        format_sql: false
        use_sql_comments: false

# 日志配置 - 生产环境
logging:
  level:
    root: INFO
    com.jcloud: INFO
    org.springframework.security: WARN
    org.springframework.web: WARN
    com.alibaba.druid: WARN
    org.mybatis: WARN
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n"
  file:
    name: ${LOG_PATH:/app/logs}/jcloud-admin.log
    max-size: 100MB
    max-history: 30
    total-size-cap: 1GB

# sa-token配置 - 生产环境
sa-token:
  token-name: ${SA_TOKEN_NAME:Authorization}
  timeout: ${SA_TOKEN_TIMEOUT:7200}
  activity-timeout: ${SA_TOKEN_ACTIVITY_TIMEOUT:1800}
  is-concurrent: ${SA_TOKEN_CONCURRENT:true}
  is-share: ${SA_TOKEN_SHARE:true}
  token-style: ${SA_TOKEN_STYLE:uuid}
  is-log: false
  is-print: false
  jwt-secret-key: ${SA_TOKEN_JWT_SECRET:jcloud-jwt-secret-key-prod-2024}
  
  # Redis集成配置
  dao-type: redis
  redis:
    database: ${REDIS_DATABASE:10}
    host: ${REDIS_HOST:localhost}
    port: ${REDIS_PORT:6379}
    password: ${REDIS_PASSWORD:}
    timeout: 10000

# MyBatis-Flex配置 - 生产环境
mybatis-flex:
  mapper-locations: classpath*:/mapper/*.xml
  type-aliases-package: com.jcloud.**.entity
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: true
    lazy-loading-enabled: false
    multiple-result-sets-enabled: true
    use-column-label: true
    use-generated-keys: true
    auto-mapping-behavior: partial
    auto-mapping-unknown-column-behavior: none
    default-executor-type: reuse
    default-statement-timeout: 25
    default-fetch-size: 100
    safe-row-bounds-enabled: false
    local-cache-scope: session
    jdbc-type-for-null: other
    lazy-load-trigger-methods: equals,clone,hashCode,toString
    default-scripting-language: org.apache.ibatis.scripting.xmltags.XMLLanguageDriver
    call-setters-on-nulls: false
    return-instance-for-empty-row: false
    log-impl: org.apache.ibatis.logging.slf4j.Slf4jImpl
    shrink-whitespaces-in-sql: false
    nullable-on-for-each: false

# 管理端点配置 - 生产环境
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
      base-path: /actuator
  endpoint:
    health:
      show-details: when-authorized
      show-components: when-authorized
  health:
    redis:
      enabled: true
    db:
      enabled: true
  metrics:
    export:
      prometheus:
        enabled: ${PROMETHEUS_ENABLED:false}

# jCloud业务配置 - 生产环境
jcloud:
  # 验证码配置
  captcha:
    enabled: ${CAPTCHA_ENABLED:true}
    type: ${CAPTCHA_TYPE:ALPHANUMERIC}
    length: ${CAPTCHA_LENGTH:4}
    expire-time: ${CAPTCHA_EXPIRE:300}
    case-sensitive: ${CAPTCHA_CASE_SENSITIVE:false}
    image:
      width: 120
      height: 40
      font-name: Arial
      font-size: 25
      line-count: 5
      noise-count: 50
      background-color: "255,255,255"
      text-color-range: "0,0,0-100,100,100"
      line-color-range: "150,150,150-200,200,200"
      noise-color-range: "100,100,100-150,150,150"
    security:
      max-verify-attempts: ${CAPTCHA_MAX_ATTEMPTS:5}
      lock-time: ${CAPTCHA_LOCK_TIME:600}
      enable-brute-force-protection: ${CAPTCHA_BRUTE_FORCE_PROTECTION:true}
      
  # 缓存配置
  cache:
    enabled: true
    type: redis
    redis:
      time-to-live: ${CACHE_TTL:3600}
      cache-null-values: false
      key-prefix: "jcloud:cache:"
      use-key-prefix: true
    cache-names:
      - userCache
      - roleCache
      - permissionCache
      - tenantCache
      - menuCache
      - deptCache

  # 文件上传配置
  file:
    upload-path: ${FILE_UPLOAD_PATH:/app/uploads}
    max-file-size: ${FILE_MAX_SIZE:10MB}
    allowed-extensions: ${FILE_ALLOWED_EXTENSIONS:jpg,jpeg,png,gif,pdf,doc,docx,xls,xlsx}
    
  # 安全配置
  security:
    # 密码策略
    password:
      min-length: ${PASSWORD_MIN_LENGTH:8}
      require-uppercase: ${PASSWORD_REQUIRE_UPPERCASE:true}
      require-lowercase: ${PASSWORD_REQUIRE_LOWERCASE:true}
      require-digit: ${PASSWORD_REQUIRE_DIGIT:true}
      require-special: ${PASSWORD_REQUIRE_SPECIAL:false}
    # 登录安全
    login:
      max-attempts: ${LOGIN_MAX_ATTEMPTS:5}
      lock-time: ${LOGIN_LOCK_TIME:1800}
      enable-captcha-after-attempts: ${LOGIN_CAPTCHA_AFTER_ATTEMPTS:3}

# Knife4j配置 - 生产环境
knife4j:
  enable: ${API_DOC_ENABLED:false}
  openapi:
    title: jCloud权限管理系统API文档
    description: 企业级权限管理系统接口文档
    version: 1.0.0
    concat: ${API_DOC_CONTACT:<EMAIL>}
    license: MIT
    license-url: https://opensource.org/licenses/MIT
  setting:
    language: zh_cn
    enable-swagger-models: true
    enable-document-manage: false
    swagger-model-name: 实体类列表
    enable-version: false
    enable-reload-cache-parameter: false
    enable-after-script: false
    enable-filter-multipart-api-method-type: POST
    enable-filter-multipart-apis: false
    enable-request-cache: true
    enable-host: false
    enable-host-text: ${API_DOC_HOST:}
