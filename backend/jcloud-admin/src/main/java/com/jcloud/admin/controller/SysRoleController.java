package com.jcloud.admin.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.jcloud.admin.service.SysRoleService;
import com.jcloud.common.annotation.OperLog;
import com.jcloud.common.dto.RoleCreateRequest;
import com.jcloud.common.dto.RoleQueryRequest;
import com.jcloud.common.dto.RoleUpdateRequest;
import com.jcloud.common.entity.SysRole;

import com.jcloud.common.entity.SysMenu;
import com.jcloud.common.enums.BusinessType;
import com.jcloud.common.page.PageResult;
import com.jcloud.common.result.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 角色管理控制器
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Tag(name = "角色管理", description = "角色管理相关接口")
@RestController
@RequestMapping("/system/role")
@RequiredArgsConstructor
public class SysRoleController {
    
    private final SysRoleService roleService;
    
    @Operation(summary = "分页查询角色列表", description = "根据条件分页查询角色列表")
    @SaCheckPermission("system:role:list")
    @OperLog(title = "角色管理", businessType = 0)
    @GetMapping("/page")
    public Result<PageResult<SysRole>> pageRoles(@Valid RoleQueryRequest queryRequest) {
        PageResult<SysRole> pageResult = roleService.pageRoles(queryRequest);
        return Result.success("查询角色列表成功", pageResult);
    }
    
    @Operation(summary = "获取角色详情", description = "根据角色ID获取角色详细信息")
    @SaCheckPermission("system:role:query")
    @OperLog(title = "角色管理", businessType = 0)
    @GetMapping("/{id}")
    public Result<SysRole> getRoleById(@Parameter(description = "角色ID") @PathVariable("id") Long id) {
        SysRole role = roleService.getById(id);
        if (role == null) {
            return Result.error("角色不存在");
        }
        return Result.success("获取角色详情成功", role);
    }
    
    @Operation(summary = "创建角色", description = "创建新角色")
    @SaCheckPermission("system:role:add")
    @OperLog(title = "角色管理", businessType = 1)
    @PostMapping
    public Result<Void> createRole(@Valid @RequestBody RoleCreateRequest createRequest) {
        boolean success = roleService.createRole(createRequest);
        if (success) {
            return Result.<Void>success("创建角色成功", null);
        } else {
            return Result.error("创建角色失败");
        }
    }
    
    @Operation(summary = "更新角色", description = "更新角色信息")
    @SaCheckPermission("system:role:edit")
    @OperLog(title = "角色管理", businessType = 2)
    @PutMapping
    public Result<Void> updateRole(@Valid @RequestBody RoleUpdateRequest updateRequest) {
        boolean success = roleService.updateRole(updateRequest);
        if (success) {
            return Result.<Void>success("更新角色成功", null);
        } else {
            return Result.error("更新角色失败");
        }
    }
    
    @Operation(summary = "删除角色", description = "根据角色ID删除角色（逻辑删除）")
    @SaCheckPermission("system:role:remove")
    @OperLog(title = "角色管理", businessType = 3)
    @DeleteMapping("/{id}")
    public Result<Void> deleteRole(@Parameter(description = "角色ID") @PathVariable("id") Long id) {
        boolean success = roleService.deleteRole(id);
        if (success) {
            return Result.<Void>success("删除角色成功", null);
        } else {
            return Result.error("删除角色失败");
        }
    }
    
    @Operation(summary = "批量删除角色", description = "根据角色ID列表批量删除角色")
    @SaCheckPermission("system:role:remove")
    @DeleteMapping("/batch")
    public Result<Void> deleteRolesBatch(@Parameter(description = "角色ID列表") @RequestBody List<Long> roleIds) {
        boolean success = roleService.deleteRolesBatch(roleIds);
        if (success) {
            return Result.<Void>success("批量删除角色成功", null);
        } else {
            return Result.error("批量删除角色失败");
        }
    }
    
    @Operation(summary = "启用/禁用角色", description = "更新角色状态")
    @SaCheckPermission("system:role:edit")
    @PutMapping("/{id}/status")
    public Result<Void> updateRoleStatus(
            @Parameter(description = "角色ID") @PathVariable("id") Long id,
            @Parameter(description = "状态（0-禁用，1-启用）") @RequestParam("status") Integer status) {
        boolean success = roleService.updateRoleStatus(id, status);
        if (success) {
            return Result.<Void>success("更新角色状态成功", null);
        } else {
            return Result.error("更新角色状态失败");
        }
    }
    
    @Operation(summary = "批量启用/禁用角色", description = "批量更新角色状态")
    @SaCheckPermission("system:role:edit")
    @PutMapping("/batch/status")
    public Result<Void> updateRoleStatusBatch(
            @Parameter(description = "角色ID列表") @RequestParam List<Long> roleIds,
            @Parameter(description = "状态（0-禁用，1-启用）") @RequestParam Integer status) {
        boolean success = roleService.updateRoleStatusBatch(roleIds, status);
        if (success) {
            return Result.<Void>success("批量更新角色状态成功", null);
        } else {
            return Result.error("批量更新角色状态失败");
        }
    }
    
    // 注意：权限分配功能已整合到菜单分配中，不再需要独立的权限分配接口
    
    @Operation(summary = "获取所有启用的角色", description = "获取当前租户下所有启用的角色列表")
    @SaCheckPermission("system:role:list")
    @GetMapping("/enabled")
    public Result<List<SysRole>> getAllEnabledRoles() {
        List<SysRole> roles = roleService.getAllEnabledRoles();
        return Result.success("获取启用角色列表成功", roles);
    }
    
    @Operation(summary = "统计角色用户数量", description = "统计指定角色下的用户数量")
    @SaCheckPermission("system:role:query")
    @GetMapping("/{id}/user-count")
    public Result<Integer> countUsersByRoleId(@Parameter(description = "角色ID") @PathVariable("id") Long id) {
        int count = roleService.countUsersByRoleId(id);
        return Result.success("获取角色用户数量成功", count);
    }
    
    @Operation(summary = "检查角色编码是否存在", description = "检查角色编码是否已存在")
    @SaCheckPermission("system:role:query")
    @GetMapping("/check-code")
    public Result<Boolean> checkRoleCode(
            @Parameter(description = "角色编码") @RequestParam String roleCode,
            @Parameter(description = "排除的角色ID") @RequestParam(required = false) Long excludeId) {
        boolean exists = roleService.isRoleCodeExists(roleCode, excludeId);
        return Result.success("检查角色编码完成", exists);
    }
    
    @Operation(summary = "检查角色名称是否存在", description = "检查角色名称是否已存在")
    @SaCheckPermission("system:role:query")
    @GetMapping("/check-name")
    public Result<Boolean> checkRoleName(
            @Parameter(description = "角色名称") @RequestParam String roleName,
            @Parameter(description = "排除的角色ID") @RequestParam(required = false) Long excludeId) {
        boolean exists = roleService.isRoleNameExists(roleName, excludeId);
        return Result.success("检查角色名称完成", exists);
    }

    // ==================== 菜单分配相关接口 ====================

    @Operation(summary = "为角色分配菜单", description = "为指定角色分配菜单权限")
    @SaCheckPermission("system:role:assign-menus")
    @OperLog(title = "角色管理", businessType =4 )
    @PostMapping("/{id}/menus")
    public Result<Void> assignMenus(
            @Parameter(description = "角色ID") @PathVariable("id") Long id,
            @Parameter(description = "菜单ID列表") @RequestBody List<Long> menuIds) {
        roleService.assignMenus(id, menuIds);
        return Result.success("菜单分配成功",null);
    }

    @Operation(summary = "获取角色菜单列表", description = "获取指定角色已分配的菜单列表")
    @SaCheckPermission("system:role:query")
    @OperLog(title = "角色管理", businessType = 0)
    @GetMapping("/{id}/menus")
    public Result<List<SysMenu>> getRoleMenus(
            @Parameter(description = "角色ID") @PathVariable("id") Long id) {
        List<SysMenu> menus = roleService.getRoleMenus(id);
        return Result.success("获取角色菜单成功", menus);
    }

    @Operation(summary = "获取角色菜单ID列表", description = "获取指定角色已分配的菜单ID列表")
    @SaCheckPermission("system:role:query")
    @OperLog(title = "角色管理", businessType = 0)
    @GetMapping("/{id}/menu-ids")
    public Result<List<Long>> getRoleMenuIds(
            @Parameter(description = "角色ID") @PathVariable("id") Long id) {
        List<Long> menuIds = roleService.getRoleMenuIds(id);
        return Result.success("获取角色菜单ID成功", menuIds);
    }
}
